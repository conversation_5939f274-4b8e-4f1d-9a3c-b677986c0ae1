import { apiClient } from '@/lib/api'
import type { TimeEntry, ConsultantInfo } from '@/features/timesheet/types'

export interface CreateTimeEntryData {
  date: string
  activity: string
  comment?: string
  hours: number
  remoteWork: boolean
  consultantId: string
}

export interface UpdateTimeEntryData extends Partial<CreateTimeEntryData> {
  id: string
}

/**
 * Add a new time tracking entry
 */
export const addTimeEntry = async (data: CreateTimeEntryData): Promise<TimeEntry> => {
  const response = await apiClient.post<TimeEntry>('/v1/work-time', data)
  return response.data
}

/**
 * Get all time entries for a specific consultant
 */
export const getConsultantTimeEntries = async (consultantId: string): Promise<TimeEntry[]> => {
  const response = await apiClient.get<TimeEntry[]>(`/v1/work-time/consultant/${consultantId}`)
  return response.data
}

/**
 * Get time entries for a consultant within a date range
 */
export const getConsultantTimeEntriesInRange = async (
  consultantId: string,
  startDate: string,
  endDate: string
): Promise<TimeEntry[]> => {
  const response = await apiClient.get<TimeEntry[]>(
    `/v1/work-time/consultant/${consultantId}?start=${startDate}&end=${endDate}`
  )
  return response.data
}

/**
 * Update an existing time entry
 */
export const updateTimeEntry = async (data: UpdateTimeEntryData): Promise<TimeEntry> => {
  const { id, ...updateData } = data
  const response = await apiClient.put<TimeEntry>(`/v1/work-time/${id}`, updateData)
  return response.data
}

/**
 * Delete a time entry
 */
export const deleteTimeEntry = async (id: string): Promise<void> => {
  await apiClient.delete(`/v1/work-time/${id}`)
}

/**
 * Get consultant information from localStorage
 */
export const getConsultantInfo = (): ConsultantInfo | null => {
  try {
    const consultantInfoStr = localStorage.getItem('consultantInfo')
    if (!consultantInfoStr) {
      return null
    }
    return JSON.parse(consultantInfoStr) as ConsultantInfo
  } catch (error) {
    console.error('Error parsing consultant info:', error)
    return null
  }
}

/**
 * Batch create multiple time entries
 */
export const batchCreateTimeEntries = async (entries: CreateTimeEntryData[]): Promise<TimeEntry[]> => {
  // Since the API might not support batch creation, we'll create them one by one
  const results: TimeEntry[] = []

  for (const entry of entries) {
    try {
      const result = await addTimeEntry(entry)
      results.push(result)
    } catch (error) {
      // If one fails, we still want to continue with the others
      console.error('Failed to create time entry:', error)
      throw error // Re-throw to handle in the calling code
    }
  }

  return results
}

/**
 * Get time entries statistics for a given period
 */
export const getTimeEntryStats = (entries: TimeEntry[]) => {
  const totalHours = entries.reduce((sum, entry) => sum + entry.hours, 0)
  const remoteHours = entries
    .filter(entry => entry.location === 'home')
    .reduce((sum, entry) => sum + entry.hours, 0)
  const onsiteHours = totalHours - remoteHours

  // Count unique days
  const uniqueDays = new Set(entries.map(entry => entry.date)).size

  return {
    totalHours,
    remoteHours,
    onsiteHours,
    totalDays: uniqueDays,
    remotePercentage: totalHours > 0 ? (remoteHours / totalHours) * 100 : 0,
    onsitePercentage: totalHours > 0 ? (onsiteHours / totalHours) * 100 : 0,
  }
}
